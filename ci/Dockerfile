# Simple single-stage build for NestJS backend
FROM node:22-alpine

# Install curl for health checks and pnpm
RUN apk add --no-cache curl && \
    corepack enable && \
    corepack prepare pnpm@latest --activate

WORKDIR /app

# Accept build arguments
ARG GITHUB_TOKEN

# Create .npmrc with GitHub Package Registry configuration
RUN echo "@sw-ecom360:registry=https://npm.pkg.github.com" > .npmrc && \
    echo "//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}" >> .npmrc

# Create non-root user for better security
RUN addgroup --system --gid 1001 appgroup && \
    adduser --system --uid 1001 appuser

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install all dependencies (dev + prod for build)
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Generate Prisma client
RUN pnpm prisma:generate

# Build the application
RUN pnpm run build

# Remove dev dependencies to reduce image size
RUN pnpm prune --prod

# Change ownership to non-root user
RUN chown -R appuser:appgroup /app

USER appuser

# Set default environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV MICROSERVICE_PORT=3003
ENV TZ=UTC

# Expose both HTTP and microservice ports
EXPOSE 3000 3003

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/api/pricingmanagement/health || exit 1

# Start the application
CMD ["node", "dist/src/main.js"]