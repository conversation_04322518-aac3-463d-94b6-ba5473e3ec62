COMPOSE_PROJECT_NAME=be-pricing-management
PORT=3000
TZ=UTC

# Microservice Configuration
MICROSERVICE_PORT=3003
SERVICE_DISCOVERY_NAMESPACE=ecom360-production.local
SERVICE_NAME=be-pricing-management-microservice

# Database Configuration
DATABASE_URL=postgresql://db-user:db-password@localhost:5432/db-pricing-management?schema=public

# AWS Cognito Configuration
COGNITO_USER_POOL_ID=eu-central-1_AfefViahW
COGNITO_CLIENT_ID=3t722ht5500hc17mj455q9v44p
COGNITO_REGION=eu-central-1

# Production Note:
# DATABASE_URL is provided via Secrets Manager in production
# CloudFormation constructs the full connection string automatically

# Queue Configuration
QUEUE_PORT=9324
QUEUE_MGMT_PORT=9325
UPLOAD_SQS_ENDPOINT=http://localhost:${QUEUE_PORT}/queue
AWS_REGION=eu-central-1

# Shopify Admin API Configuration
SHOPIFY_CONFIG_PATH=./config/shops.yaml

# Optional Configuration Overrides
# SHOPIFY_API_VERSION=2025-07
# SHOPIFY_RETRIES=0
# SHOPIFY_USER_AGENT_PREFIX=SWECOM360/1.0

# Shopify Admin API Access Tokens (format: SHOPIFY_ACCESS_TOKEN_<SHOP_KEY_UPPERCASE>)
SHOPIFY_ACCESS_TOKEN_DEV1=
SHOPIFY_ACCESS_TOKEN_DEV2=
SHOPIFY_ACCESS_TOKEN_DEV3=
