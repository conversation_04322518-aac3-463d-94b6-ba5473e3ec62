name: Ecom360 BE Pricing Management Infrastructure

on:
  push:
    branches: [main]

  pull_request:
    branches: [main]

permissions:
  id-token: write
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: pip install cfn-lint
      - run: |
          cfn-lint ci/aws/ecr.yaml
          cfn-lint ci/aws/be-pricing-management.yaml
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    needs: lint
    environment: production
    steps:
      - uses: actions/checkout@v4
      - uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::586794460318:role/GitHubDeployRole
          aws-region: eu-central-1
      - run: chmod +x ./ci/scripts/cloudformation/*.sh
      - name: Deploy ECR
        run: ./ci/scripts/cloudformation/deploy-ecr.sh eu-central-1 production
      - name: Deploy Application
        run: ./ci/scripts/cloudformation/deploy-be-pricing-management.sh eu-central-1 production build-${{ github.run_id }}
      - name: Run Database Migrations
        run: ./ci/scripts/run-migrations.sh eu-central-1 production
