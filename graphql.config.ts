import { ApiVersion } from "@shopify/shopify-api";
import { createShopifyCodegenConfig } from "@sw-ecom360/shopify-admin-api";
import type { IGraphQLConfig } from "graphql-config";

const config: IGraphQLConfig = {
  ...createShopifyCodegenConfig({
    apiVersion: ApiVersion.July25,
    documents: ["./src/**/*.{js,ts,jsx,tsx}"],
    outputDir: "./generated/shopify/types",
  }),
};

export default config;
