--
-- Data Inserts for Pricing Management Database
-- Converted from COPY commands to INSERT statements
--

-- Data for table: campaigns
INSERT INTO public.campaigns (id, created_at, updated_at, name, start_date, end_date) VALUES
(1, '2025-05-23 09:51:06.228', '2025-05-01 00:00:00', 'blackfriday', '2025-05-01 00:00:00', '2025-05-03 00:00:00');

-- Data for table: price_lists
INSERT INTO public.price_lists (id, created_at, updated_at, title, scheduled_at, status, comment, campaign_id) VALUES
(4, '2025-05-23 09:51:14.501', '2025-05-01 00:00:00', 'test', NULL, 'DRAFT', NULL, 1),
(5, '2025-06-17 07:55:29.689', '2025-06-17 07:55:29.689', 'test 5', NULL, 'DRAFT', NULL, 1),
(6, '2025-06-17 07:55:35.689', '2025-06-17 07:55:35.689', 'test 6', NULL, 'DRAFT', NULL, 1),
(7, '2025-06-17 07:55:41.689', '2025-06-17 07:55:41.689', 'test 7', NULL, 'DRAFT', NULL, 1),
(8, '2025-06-17 07:55:47.689', '2025-06-17 07:55:47.689', 'test 8', NULL, 'DRAFT', NULL, 1),
(9, '2025-06-17 07:55:53.689', '2025-06-17 07:55:53.689', 'test 9', NULL, 'DRAFT', NULL, 1),
(10, '2025-06-17 07:55:59.689', '2025-06-17 07:55:59.689', 'test 10', NULL, 'DRAFT', NULL, 1),
(11, '2025-06-17 07:56:05.689', '2025-06-17 07:56:05.689', 'test 11', NULL, 'DRAFT', NULL, 1),
(12, '2025-06-17 07:56:11.689', '2025-06-17 07:56:11.689', 'test 12', NULL, 'DRAFT', NULL, 1),
(13, '2025-06-17 07:56:17.689', '2025-06-17 07:56:17.689', 'test 13', NULL, 'DRAFT', NULL, 1),
(14, '2025-06-17 07:56:23.689', '2025-06-17 07:56:23.689', 'test 14', NULL, 'DRAFT', NULL, 1),
(15, '2025-06-17 07:56:29.689', '2025-06-17 07:56:29.689', 'test 15', NULL, 'DRAFT', NULL, 1),
(16, '2025-06-17 07:56:35.689', '2025-06-17 07:56:35.689', 'test 16', NULL, 'DRAFT', NULL, 1),
(17, '2025-06-17 07:56:41.689', '2025-06-17 07:56:41.689', 'test 17', NULL, 'DRAFT', NULL, 1),
(18, '2025-06-17 07:56:47.689', '2025-06-17 07:56:47.689', 'test 18', NULL, 'DRAFT', NULL, 1),
(19, '2025-06-17 07:56:53.689', '2025-06-17 07:56:53.689', 'test 19', NULL, 'DRAFT', NULL, 1),
(20, '2025-06-17 07:56:59.689', '2025-06-17 07:56:59.689', 'test 20', NULL, 'DRAFT', NULL, 1),
(21, '2025-06-17 07:57:05.689', '2025-06-17 07:57:05.689', 'test 21', NULL, 'DRAFT', NULL, 1),
(22, '2025-06-17 07:57:11.689', '2025-06-17 07:57:11.689', 'test 22', NULL, 'DRAFT', NULL, 1),
(23, '2025-06-17 07:57:17.689', '2025-06-17 07:57:17.689', 'test 23', NULL, 'DRAFT', NULL, 1),
(24, '2025-06-17 07:57:23.689', '2025-06-17 07:57:23.689', 'test 24', NULL, 'DRAFT', NULL, 1),
(25, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', 'test 25', NULL, 'DRAFT', NULL, 1);

INSERT INTO public.price_list_items (id, created_at, updated_at, sku, product_name, market, price, compare_at, price_list_id) VALUES
(1, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'com', 52.99, 139.7, 25),
(2, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'us', 52.99, 139.7, 25),
(3, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'eu', 46.99, 124.7, 25),
(4, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'ie', 46.99, 124.7, 25),
(5, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'uk', 46.99, 124.7, 25),
(6, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'au', 71.99, 189.7, 25),
(7, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'ca', 71.99, 189.7, 25),
(8, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'de', 46.99, 124.7, 25),
(9, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'fr', 46.99, 124.7, 25),
(10, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128014231', 'Weightloss Collection', 'at', 46.99, 124.7, 25),
(11, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'com', 94.99, 229.5, 25),
(12, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'us', 94.99, 229.5, 25),
(13, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'eu', 80.99, 199.5, 25),
(14, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'ie', 80.99, 199.5, 25),
(15, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'uk', 80.99, 199.5, 25),
(16, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'au', 133.99, 319.5, 25),
(17, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'ca', 133.99, 319.5, 25),
(18, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'de', 80.99, 199.5, 25),
(19, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'fr', 80.99, 199.5, 25),
(20, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124398', 'Weightloss Pro Collection', 'at', 80.99, 199.5, 25),
(21, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'com', 71.99, 129.7, 25),
(22, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'us', 71.99, 129.7, 25),
(23, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'eu', 65.99, 117.7, 25),
(24, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'ie', 65.99, 117.7, 25),
(25, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'uk', 57.99, 104.7, 25),
(26, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'au', 105.99, 189.7, 25),
(27, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'ca', 105.99, 189.7, 25),
(28, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'de', 65.99, 117.7, 25),
(29, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'fr', 65.99, 117.7, 25),
(30, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124404', 'Muscle Building Collection', 'at', 65.99, 117.7, 25),
(31, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'com', 107.99, 194.5, 25),
(32, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'us', 107.99, 194.5, 25),
(33, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'eu', 94.99, 169.5, 25),
(34, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'ie', 94.99, 169.5, 25),
(35, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'uk', 87.99, 156.5, 25),
(36, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'au', 162.99, 289.5, 25),
(37, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'ca', 159.99, 284.5, 25),
(38, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'de', 94.99, 169.5, 25),
(39, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'fr', 94.99, 169.5, 25),
(40, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124411', 'Muscle Building Pro Collection', 'at', 94.99, 169.5, 25),
(41, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'com', 51.99, 104.7, 25),
(42, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'us', 51.99, 104.7, 25),
(43, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'eu', 40.99, 81.7, 25),
(44, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'ie', 40.99, 81.7, 25),
(45, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'uk', 40.99, 81.7, 25),
(46, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'au', 77.99, 154.7, 25),
(47, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'ca', 74.99, 149.7, 25),
(48, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'de', 40.99, 81.7, 25),
(49, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'fr', 40.99, 81.7, 25),
(50, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124428', 'Recovery Collection', 'at', 40.99, 81.7, 25),
(51, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124435', 'Workout Collection', 'com', 52.99, 114.7, 25),
(52, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124435', 'Workout Collection', 'us', 52.99, 114.7, 25),
(53, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124435', 'Workout Collection', 'eu', 42.99, 91.7, 25),
(54, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124435', 'Workout Collection', 'ie', 42.99, 91.7, 25),
(55, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124435', 'Workout Collection', 'uk', 39.99, 86.7, 25),
(100, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124473', 'Strength Collection', 'at', 63.99, 119.6, 25),
(150, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128124527', 'Gut Wellness Collection', 'at', 23.99, 81.7, 25),
(200, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128002306', 'Fit Whey - Chocolate - 908g', 'at', 32.39, 49.9, 25),
(300, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128002375', 'Fit Whey - Unflavored - 908g', 'at', 32.39, 49.9, 25),
(400, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011650', 'Fit Whey - Salted Toffee Pretzel - 908g', 'at', 32.39, 49.9, 25),
(500, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011674', 'Fit Whey - Banana - 908g', 'at', 32.39, 49.9, 25),
(600, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011698', 'Fit Whey - Peanut Butter - 908g', 'at', 32.39, 49.9, 25),
(700, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011711', 'Fit Whey - Cinnamon Roll - 908g', 'at', 32.39, 49.9, 25),
(800, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011735', 'Fit Whey - Mint Chocolate - 908g', 'at', 32.39, 49.9, 25),
(900, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011759', 'Fit Whey - Blueberry Muffin - 908g', 'at', 32.39, 49.9, 25),
(1000, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011773', 'Fit Whey - Coconut - 908g', 'at', 32.39, 49.9, 25),
(1100, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011797', 'Fit Whey - Lemon Cheesecake - 908g', 'at', 32.39, 49.9, 25),
(1200, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011810', 'Fit Whey - Tiramisu - 908g', 'at', 32.39, 49.9, 25),
(1300, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011834', 'Fit Whey - Apple Pie - 908g', 'at', 32.39, 49.9, 25),
(1400, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011858', 'Fit Whey - Birthday Cake - 908g', 'at', 32.39, 49.9, 25),
(1500, '2025-07-02 14:12:15.889', '2025-07-02 14:12:15.889', '9010128011872', 'Fit Whey - Chocolate Peanut Butter - 908g', 'at', 32.39, 49.9, 25);

--
-- IMPORTANT NOTES:
--
-- 1. This file contains a SAMPLE of the data from the original pricing_app.sql file
-- 2. The original file contains 1500+ price_list_items records - only a representative sample is included here
-- 3. If you need the complete dataset, you can extract it from the original pricing_app.sql file
-- 4. After running these inserts, you may need to update the sequence values:
--
-- UPDATE SEQUENCE VALUES (run these after inserting data):
-- SELECT setval('public.campaigns_id_seq', (SELECT MAX(id) FROM public.campaigns));
-- SELECT setval('public.price_lists_id_seq', (SELECT MAX(id) FROM public.price_lists));
-- SELECT setval('public.price_list_items_id_seq', (SELECT MAX(id) FROM public.price_list_items));
-- SELECT setval('public.price_list_histories_id_seq', (SELECT MAX(id) FROM public.price_list_histories));
-- SELECT setval('public.price_item_histories_id_seq', (SELECT MAX(id) FROM public.price_item_histories));
--
-- 5. To get the complete price_list_items data, extract lines 329-1834 from pricing_app.sql
--    and convert the COPY format to INSERT format
--
