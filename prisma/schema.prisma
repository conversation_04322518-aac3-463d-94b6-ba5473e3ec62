// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Campaign {
  id          Int         @id @default(autoincrement())
  created_at  DateTime    @default(now())
  updated_at  DateTime    @updatedAt
  name        String
  start_date  DateTime
  end_date    DateTime?
  price_lists PriceList[]

  @@map("campaigns")
}

model PriceList {
  id                   Int                @id @default(autoincrement())
  created_at           DateTime           @default(now())
  updated_at           DateTime           @updatedAt
  title                String
  scheduled_at         DateTime?
  status               String             @default("DRAFT")
  comment              String?
  campaign_id          Int
  campaign             Campaign           @relation(fields: [campaign_id], references: [id])
  price_list_histories PriceListHistory[]
  price_list_items     PriceListItem[]

  @@map("price_lists")
}

model PriceListHistory {
  id             Int                @id @default(autoincrement())
  created_at     DateTime           @default(now())
  updated_at     DateTime           @updatedAt
  status         String
  published_at   DateTime
  price_list_id  Int
  price_list     PriceList          @relation(fields: [price_list_id], references: [id], onDelete: Cascade)
  item_histories PriceItemHistory[]

  @@map("price_list_histories")
}

model PriceItemHistory {
  id                    Int              @id @default(autoincrement())
  created_at            DateTime         @default(now())
  updated_at            DateTime         @updatedAt
  status                String
  price                 Float?
  compare_at            Float?
  market                String
  price_list_history_id Int
  price_list_history    PriceListHistory @relation(fields: [price_list_history_id], references: [id], onDelete: Cascade)
  price_list_item_id    Int
  price_list_item       PriceListItem    @relation(fields: [price_list_item_id], references: [id], onDelete: Cascade)

  @@map("price_item_histories")
}

model PriceListItem {
  id             Int                      @id @default(autoincrement())
  created_at     DateTime                 @default(now())
  updated_at     DateTime                 @updatedAt
  sku            String
  product_name   String
  market         String
  price          Float
  compare_at     Float
  price_list_id  Int
  search_vector  Unsupported("tsvector")?
  price_list     PriceList                @relation(fields: [price_list_id], references: [id], onDelete: Cascade)
  item_histories PriceItemHistory[]

  @@index(fields: [search_vector], name: "price_list_items_search_vector_idx", type: Gin)
  @@map("price_list_items")
}
