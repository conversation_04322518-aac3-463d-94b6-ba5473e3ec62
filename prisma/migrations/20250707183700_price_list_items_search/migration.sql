ALTER TABLE "price_list_items" ADD COLUMN search_vector tsvector;

-- Create a function to update the search vector
CREATE OR REPLACE FUNCTION price_list_items_search_vector_update() RET<PERSON><PERSON> trigger AS $$
BEGIN
  NEW.search_vector = to_tsvector('english', coalesce(NEW.product_name, '') || ' ' || coalesce(NEW.sku, ''));
  RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the search vector
CREATE TRIGGER price_list_items_search_vector_update
BEFORE INSERT OR UPDATE ON "price_list_items"
FOR EACH ROW EXECUTE FUNCTION price_list_items_search_vector_update();

UPDATE "price_list_items" SET search_vector = to_tsvector('english', coalesce(product_name, '') || ' ' || coalesce(sku, ''));

-- Create a GIN index on the search vector
CREATE INDEX price_list_items_search_vector_idx ON "price_list_items" USING GIN (search_vector);
