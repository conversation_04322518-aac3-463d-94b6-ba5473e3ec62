import { Resolver, Query, Mutation, Args } from "@nestjs/graphql";
import { PaginationInput } from "src/common/dto/pagination.input";
import { CampaignWhereInput } from "./dto/campaign-where.input";
import { CreateCampaignInput } from "./dto/create-campaign.input";
import { UpdateCampaignInput } from "./dto/update-campaign.input";
import { CampaignService } from "./services/campaign.service";

@Resolver("Campaign")
export class CampaignResolver {
  constructor(private readonly campaignService: CampaignService) {}

  @Mutation("createCampaign")
  create(
    @Args("createCampaignInput") createCampaignInput: CreateCampaignInput,
  ) {
    return this.campaignService.create(createCampaignInput);
  }

  @Query("campaigns")
  findAll(
    @Args("where")
    where: CampaignWhereInput,
    @Args("pagination")
    pagination: PaginationInput,
  ) {
    return this.campaignService.findAllPaginated(pagination, where);
  }

  @Query("campaign")
  findOne(@Args("id") id: number) {
    return this.campaignService.findOne(id);
  }

  @Query("activeCampaigns")
  findActiveCampaigns() {
    return this.campaignService.findActiveCampaigns();
  }

  @Mutation("updateCampaign")
  update(
    @Args("updateCampaignInput") updateCampaignInput: UpdateCampaignInput,
  ) {
    return this.campaignService.update(
      updateCampaignInput.id,
      updateCampaignInput,
    );
  }

  @Mutation("removeCampaign")
  remove(@Args("id") id: number) {
    return this.campaignService.remove(id);
  }
}
