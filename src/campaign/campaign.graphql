type Campaign {
  id: Int!
  name: String!
  startDate: DateTime!
  endDate: DateTime
  priceLists: [PriceList!]
}

input CreateCampaignInput {
  name: String!
  startDate: DateTime!
  endDate: DateTime
}

input UpdateCampaignInput {
  id: Int!
  name: String
  startDate: DateTime
  endDate: DateTime
}

input CampaignWhereInput {
  endDate: WhereInput
}

type PaginatedCampaign {
  data: [Campaign!]!
  meta: PaginationMeta!
}

type Query {
  campaigns(
    pagination: PaginationInput!
    where: CampaignWhereInput
  ): PaginatedCampaign!
  campaign(id: Int!): Campaign
  activeCampaigns: [Campaign!]!
}

type Mutation {
  createCampaign(createCampaignInput: CreateCampaignInput!): Campaign!
  updateCampaign(updateCampaignInput: UpdateCampaignInput!): Campaign!
  removeCampaign(id: Int!): Campaign
}
