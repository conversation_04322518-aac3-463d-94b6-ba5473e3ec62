import { Injectable } from "@nestjs/common";
import { PaginationInput } from "src/common/dto/pagination.input";
import { PaginationService } from "src/common/services/pagination.service";
import { Campaign } from "src/models/campaign.entity";
import { CampaignWhereInput } from "../dto/campaign-where.input";
import { CreateCampaignInput } from "../dto/create-campaign.input";
import { PaginatedCampaign } from "../dto/paginated-campaign.dto";
import { UpdateCampaignInput } from "../dto/update-campaign.input";
import { CampaignRepository } from "../repositories/campaign.repository";

@Injectable()
export class CampaignService {
  constructor(
    private readonly campaignRepository: CampaignRepository,
    private readonly paginationService: PaginationService,
  ) {}

  create(createCampaignInput: CreateCampaignInput) {
    return this.campaignRepository.create(createCampaignInput);
  }

  async findAll(whereInput?: CampaignWhereInput) {
    return this.campaignRepository.findAll(whereInput);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput?: CampaignWhereInput,
  ): Promise<PaginatedCampaign> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);
    const { totalCount, campaigns: data } =
      await this.campaignRepository.findAllPaginated(
        paginationParams,
        whereInput,
      );

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );
    return { meta, data };
  }

  async findOne(id: number) {
    return this.campaignRepository.findOne(id);
  }

  async update(id: number, updateCampaignInput: UpdateCampaignInput) {
    return this.campaignRepository.update(id, updateCampaignInput);
  }

  async remove(id: number) {
    return this.campaignRepository.remove(id);
  }

  async findActiveCampaigns(): Promise<Campaign[]> {
    return this.campaignRepository.findActiveCampaigns();
  }
}
