import { Injectable } from "@nestjs/common";
import { plainToInstance } from "class-transformer";
import { PaginationParameters } from "src/common/dto/pagination-parameters.dto";
import { PrismaService } from "src/common/services/prisma.service";
import { QueryMapperService } from "src/common/services/query-mapper.service";
import { Campaign } from "src/models/campaign.entity";
import { CampaignWhereInput } from "../dto/campaign-where.input";
import { CreateCampaignInput } from "../dto/create-campaign.input";
import { UpdateCampaignInput } from "../dto/update-campaign.input";

@Injectable()
export class CampaignRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queryMapperService: QueryMapperService,
  ) {}

  async create(createCampaignInput: CreateCampaignInput): Promise<Campaign> {
    const created = await this.prisma.campaign.create({
      data: {
        name: createCampaignInput.name,
        start_date: createCampaignInput.startDate,
        end_date: createCampaignInput.endDate,
      },
    });
    return plainToInstance(Campaign, created);
  }

  async findAll(whereInput?: CampaignWhereInput): Promise<Campaign[]> {
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput, ["id"])
      : undefined;

    const campaigns = await this.prisma.campaign.findMany({
      where,
    });
    return plainToInstance(Campaign, campaigns);
  }

  async findAllPaginated(
    pagination: PaginationParameters,
    whereInput?: CampaignWhereInput,
  ): Promise<{ campaigns: Campaign[]; totalCount: number }> {
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput, ["id"])
      : undefined;

    const query = {
      where,
    };

    const [totalCount, campaigns] = await Promise.all([
      this.prisma.campaign.count(query),
      this.prisma.campaign.findMany({
        ...query,
        ...pagination,
      }),
    ]);

    return { campaigns: plainToInstance(Campaign, campaigns), totalCount };
  }

  async findOne(id: number): Promise<Campaign | null> {
    const campaign = await this.prisma.campaign.findUnique({
      where: { id },
    });
    return campaign ? plainToInstance(Campaign, campaign) : null;
  }

  async update(
    id: number,
    updateCampaignInput: UpdateCampaignInput,
  ): Promise<Campaign> {
    const updated = await this.prisma.campaign.update({
      where: { id },
      data: {
        name: updateCampaignInput.name,
        start_date: updateCampaignInput.startDate,
        end_date: updateCampaignInput.endDate,
      },
    });
    return plainToInstance(Campaign, updated);
  }

  async remove(id: number): Promise<Campaign> {
    const removed = await this.prisma.campaign.delete({
      where: { id },
    });
    return plainToInstance(Campaign, removed);
  }

  async findActiveCampaigns(): Promise<Campaign[]> {
    const activeCampaigns = await this.prisma.campaign.findMany({
      where: {
        OR: [
          {
            end_date: null,
          },
          {
            end_date: {
              gte: new Date(),
            },
          },
        ],
      },
    });
    return plainToInstance(Campaign, activeCampaigns);
  }
}
