import { Module } from "@nestjs/common";
import { CommonModule } from "src/common/common.module";
import { CampaignResolver } from "./campaign.resolver";
import { CampaignRepository } from "./repositories/campaign.repository";
import { CampaignService } from "./services/campaign.service";

@Module({
  imports: [CommonModule],
  providers: [CampaignResolver, CampaignService, CampaignRepository],
  exports: [CampaignService],
})
export class CampaignModule {}
