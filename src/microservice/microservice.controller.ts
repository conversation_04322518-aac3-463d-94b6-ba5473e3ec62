import { Controller, Logger } from "@nestjs/common";
import { MessagePattern } from "@nestjs/microservices";

@Controller()
export class MicroserviceController {
  private readonly logger = new Logger(MicroserviceController.name);

  @MessagePattern("health_check")
  healthCheck() {
    this.logger.log("🔍 Received health_check message");
    const response = {
      hello: "world",
      status: "ok",
      timestamp: new Date().toISOString(),
    };
    this.logger.log(
      `📤 Sending health_check response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
