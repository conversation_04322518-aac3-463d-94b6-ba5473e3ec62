type PriceList {
  id: Int!
  title: String!
  createdAt: String!
  scheduledAt: DateTime
  lastPublishedAt: DateTime
  firstPublishedAt: DateTime
  comment: String
  status: String!
  campaignId: Int!
  campaign: Campaign!
  file: File
  priceListItems: [PriceListItem!]
}

type PriceListHistory {
  id: Int!
  status: String!
  publishedAt: DateTime!
  priceListId: Int
  successfulChangesCount: Int
  priceList: PriceList!
 # priceItemHistories: [PriceItemHistory!]
}

type PaginatedPriceList {
  data: [PriceList!]!
  meta: PaginationMeta!
}

input CreatePriceListInput {
  title: String!
  campaignId: Int!
  scheduledAt: String
  comment: String
  file: File!
}

input UpdatePriceListInput {
  id: ID!
  title: String
  scheduledAt: String
  status: String
  campaignId: Int
}

input PriceListWhereInput {
  campaignId: Int
}

input PriceListWhereUniqueInput {
  id: Int
}

input PriceListHistoryWhereInput {
  priceListId: Int
}

type Query {
  priceLists(
    where: PriceListWhereInput
    pagination: PaginationInput
  ): PaginatedPriceList!
  priceList(where: PriceListWhereUniqueInput!): PriceList
  priceListHistories(where: PriceListHistoryWhereInput!): [PriceListHistory!]!
}

type Mutation {
  createPriceList(createPriceListInput: CreatePriceListInput!): PriceList!
  updatePriceList(updatePriceListInput: UpdatePriceListInput!): PriceList!
  removePriceList(id: Int!): PriceList!
}
