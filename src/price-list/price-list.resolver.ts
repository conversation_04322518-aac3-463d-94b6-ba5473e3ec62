import { Inject, UsePipes, ValidationPipe } from "@nestjs/common";
import {
  Resolver,
  Query,
  Mutation,
  Args,
  Parent,
  ResolveField,
} from "@nestjs/graphql";
import { ClientProxy } from "@nestjs/microservices";
import { CampaignService } from "src/campaign/services/campaign.service";
import { PaginationInput } from "src/common/dto/pagination.input";
import { WhereOperator } from "src/common/dto/where.input";
import { PriceList } from "src/models/price-list.entity";
import { PriceListItemWhereInput } from "src/price-list-item/dto/price-list-item-where.input";
import { PriceListItemService } from "src/price-list-item/services/price-list-item.service";
import { CreatePriceListInput } from "./dto/graphql/create-price-list.input";
import { PriceListHistoryWhereInput } from "./dto/graphql/price-list-history-where.input";
import PriceListUniqueInput from "./dto/graphql/price-list-unique.input";
import { PriceListWhereInput } from "./dto/graphql/price-list-where.input";
import { UpdatePriceListInput } from "./dto/graphql/update-price-list.input";
import { PriceListHistoryService } from "./services/price-list-history.service";
import { PriceListService } from "./services/price-list.service";

@Resolver("PriceList")
export class PriceListResolver {
  constructor(
    private readonly priceListService: PriceListService,
    private readonly priceListItemService: PriceListItemService,
    private readonly campaignService: CampaignService,
    private readonly priceListHistoryService: PriceListHistoryService,

    @Inject("PRODUCTS_SERVICE") private productsService: ClientProxy,
  ) {}

  @Mutation("createPriceList")
  create(
    @Args("createPriceListInput")
    createPriceListInput: CreatePriceListInput,
  ) {
    return this.priceListService.create(createPriceListInput);
  }

  @Query("priceLists")
  @UsePipes(new ValidationPipe({ transform: true }))
  async findAll(
    @Args("where")
    where: PriceListWhereInput,
    @Args("pagination")
    pagination: PaginationInput,
  ) {
    return await this.priceListService.findAllPaginated(pagination, where);
  }

  @Query("priceList")
  findOne(@Args("where") where: PriceListUniqueInput) {
    return this.priceListService.findOne(where);
  }

  @Mutation("updatePriceList")
  update(
    @Args("updatePriceListInput") updatePriceListInput: UpdatePriceListInput,
  ) {
    return this.priceListService.update(
      updatePriceListInput.id,
      updatePriceListInput,
    );
  }

  @Mutation("removePriceList")
  remove(@Args("id") id: number) {
    return this.priceListService.remove(id);
  }

  @Query("priceListHistories")
  priceListHistories(@Args("where") where: PriceListHistoryWhereInput) {
    return this.priceListHistoryService.findAll(where);
  }

  @ResolveField("priceListItems")
  getPriceListItems(@Parent() { id }: PriceList) {
    const where: PriceListItemWhereInput = {
      priceListId: {
        operation: WhereOperator.IS,
        value: id.toString(),
      },
    };

    return this.priceListItemService.findAll(where);
  }

  @ResolveField("campaign")
  getCampaign(@Parent() { campaignId }: PriceList) {
    return this.campaignService.findOne(campaignId);
  }
}
