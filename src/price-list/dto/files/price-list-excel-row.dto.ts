import { Type } from "class-transformer";
import "reflect-metadata";
import {
  IsDefined,
  IsNotEmpty,
  IsNumber,
  IsString,
  Min,
  ValidateIf,
} from "class-validator";

type IPriceListExcelRow = InstanceType<typeof PriceListExcelRow>;

export class PriceListExcelRow {
  @IsString()
  @IsNotEmpty()
  sku: string;

  @IsString()
  @IsNotEmpty()
  product_name: string;

  @ValidateIf((o: IPriceListExcelRow) => o.us_price !== undefined)
  @IsDefined({ message: "us_compare is required when us_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "us_compare must be a number." })
  com_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.us_compare !== undefined)
  @IsDefined({ message: "us_price is required when us_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "us_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  com_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.us_price !== undefined)
  @IsDefined({ message: "us_compare is required when us_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "us_compare must be a number." })
  us_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.us_compare !== undefined)
  @IsDefined({ message: "us_price is required when us_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "us_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  us_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.eu_price !== undefined)
  @IsDefined({ message: "eu_compare is required when eu_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "eu_compare must be a number." })
  eu_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.eu_compare !== undefined)
  @IsDefined({ message: "eu_price is required when eu_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "eu_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  eu_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.ie_price !== undefined)
  @IsDefined({ message: "ie_compare is required when ie_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "ie_compare must be a number." })
  ie_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.ie_compare !== undefined)
  @IsDefined({ message: "ie_price is required when ie_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "ie_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  ie_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.uk_price !== undefined)
  @IsDefined({ message: "uk_compare is required when uk_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "uk_compare must be a number." })
  uk_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.uk_compare !== undefined)
  @IsDefined({ message: "uk_price is required when uk_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "uk_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  uk_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.au_price !== undefined)
  @IsDefined({ message: "au_compare is required when au_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "au_compare must be a number." })
  au_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.au_compare !== undefined)
  @IsDefined({ message: "au_price is required when au_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "au_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  au_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.ca_price !== undefined)
  @IsDefined({ message: "ca_compare is required when ca_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "ca_compare must be a number." })
  ca_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.ca_compare !== undefined)
  @IsDefined({ message: "ca_price is required when ca_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "ca_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  ca_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.de_price !== undefined)
  @IsDefined({ message: "de_compare is required when de_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "de_compare must be a number." })
  de_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.de_compare !== undefined)
  @IsDefined({ message: "de_price is required when de_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "de_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  de_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.fr_price !== undefined)
  @IsDefined({ message: "fr_compare is required when fr_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "fr_compare must be a number." })
  fr_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.fr_compare !== undefined)
  @IsDefined({ message: "fr_price is required when fr_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "fr_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  fr_price?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.at_price !== undefined)
  @IsDefined({ message: "at_compare is required when at_price is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "at_compare must be a number." })
  at_compare?: number;

  @ValidateIf((o: IPriceListExcelRow) => o.at_compare !== undefined)
  @IsDefined({ message: "at_price is required when at_compare is set." })
  @Type(() => Number)
  @IsNumber({}, { message: "at_price must be a number." })
  @Min(1, { message: "price must be at least 1." })
  at_price?: number;
}
