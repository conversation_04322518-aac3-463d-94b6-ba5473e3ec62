import { Injectable } from "@nestjs/common";
import { PaginationInput } from "src/common/dto/pagination.input";
import { PaginationService } from "src/common/services/pagination.service";
import { CreatePriceListInput } from "../dto/graphql/create-price-list.input";
import { PriceListHistoryWhereInput } from "../dto/graphql/price-list-history-where.input";
import PriceListUniqueInput from "../dto/graphql/price-list-unique.input";
import { PriceListWhereInput } from "../dto/graphql/price-list-where.input";
import { UpdatePriceListInput } from "../dto/graphql/update-price-list.input";
import { PaginatedPriceList } from "../dto/paginated-price-list.dto";
import { PriceListRepository } from "../repositories/price-list.repository";
import { PriceListParserService } from "./price-list-file-parser.service";

@Injectable()
export class PriceListService {
  constructor(
    private readonly priceListRepository: PriceListRepository,
    private readonly priceListParserService: PriceListParserService,
    private readonly paginationService: PaginationService,
  ) {}

  async create(createPriceListInput: CreatePriceListInput) {
    const parsedFile = await this.priceListParserService.parseExcelFile(
      createPriceListInput.file,
    );

    return this.priceListRepository.create(
      createPriceListInput,
      parsedFile.priceListItems,
    );
  }

  findAll(whereInput: PriceListWhereInput) {
    return this.priceListRepository.findAll(whereInput);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput?: PriceListWhereInput,
  ): Promise<PaginatedPriceList> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);

    const { totalCount, priceLists: data } =
      await this.priceListRepository.findAllPaginated(
        paginationParams,
        whereInput,
      );

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  async findOne(where: PriceListUniqueInput) {
    return this.priceListRepository.findOne(where);
  }

  async update(id: number, updatePriceListInput: UpdatePriceListInput) {
    return this.priceListRepository.update(id, updatePriceListInput);
  }

  async remove(id: number) {
    return this.priceListRepository.remove(id);
  }

  async getHistories(where: PriceListHistoryWhereInput) {
    return this.priceListRepository.getHistories(where);
  }
}
