import { Injectable } from "@nestjs/common";
import { PaginationService } from "src/common/services/pagination.service";
import { PriceListHistoryWhereInput } from "../dto/graphql/price-list-history-where.input";
import { PriceListRepository } from "../repositories/price-list.repository";
import { PriceListParserService } from "./price-list-file-parser.service";

@Injectable()
export class PriceListHistoryService {
  constructor(
    private readonly priceListRepository: PriceListRepository,
    private readonly priceListParserService: PriceListParserService,
    private readonly paginationService: PaginationService,
  ) {}

  findAll(whereInput: PriceListHistoryWhereInput) {
    return this.priceListRepository.getHistories(whereInput);
  }
}
