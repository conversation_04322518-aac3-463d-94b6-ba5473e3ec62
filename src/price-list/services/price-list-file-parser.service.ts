import { Injectable } from "@nestjs/common";
import { plainToInstance } from "class-transformer";
import { validate } from "class-validator";
import * as xlsx from "xlsx";
import { CreatePriceListItemInput } from "src/price-list-item/dto/price-list-item/create-price-list-item.input";
import { PriceListExcelRow } from "../dto/files/price-list-excel-row.dto";
import { PriceListRepository } from "../repositories/price-list.repository";

@Injectable()
export class PriceListParserService {
  constructor(private readonly priceListRepository: PriceListRepository) {}

  async parseExcelFile(file: File) {
    const excelData = await this.importExcel(file);
    const validatedData = await this.validateAndMap(excelData);

    const validRows = validatedData
      .filter((row) => row.errors === null)
      .map((row) => row.data);

    const invalidRows = validatedData.filter((row) => row.errors !== null);

    // TODO if infalid rows braek

    // Map valid rows to CreatePriceListItemInput[]
    const priceListItems = validRows.flatMap((row) =>
      this.mapRowToPriceListItems(row),
    );

    return {
      priceListItems,
      invalidRows,
    };
  }

  private async importExcel(file: File) {
    const buffer = await file.arrayBuffer();

    const workbook = xlsx.read(buffer, { type: "array" });
    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

    return xlsx.utils.sheet_to_json<{ [key: string]: string | number }>(
      firstSheet,
      {
        raw: false,
      },
    );
  }

  private async validateAndMap(
    excelData: Array<{ [key: string]: string | number }>,
  ) {
    return Promise.all(
      excelData.map(async (row, index) => {
        // Remove whitespace from all keys
        const cleanedRow = Object.fromEntries(
          Object.entries(row).map(([key, value]) => [
            key.replace(/\s+/g, "").toLocaleLowerCase(),
            value,
          ]),
        );

        const data = plainToInstance(PriceListExcelRow, cleanedRow);
        const errors = await validate(data, {
          whitelist: true,
          forbidNonWhitelisted: true,
        });

        return {
          row: index + 1,
          data,
          errors: errors.length > 0 ? errors : null,
        };
      }),
    );
  }

  private mapRowToPriceListItems(
    row: PriceListExcelRow,
  ): CreatePriceListItemInput[] {
    const { sku, product_name } = row;
    const priceMap = Object.entries(row).reduce<
      Record<string, Record<string, number | string>>
    >((acc, [key, value]) => {
      const match = key.match(/^([a-z]{2,3})_(compare|price)$/);

      if (!match) {
        return acc;
      }

      const [, market, type] = match;

      if (!(market in acc)) {
        acc[market] = {
          sku,
          market: market.toUpperCase(),
          product_name,
        };
      }

      if (type === "compare") {
        acc[market].compare_at = Number(value);
        return acc;
      }

      acc[market][type] = Number(value);
      return acc;
    }, {});

    return plainToInstance(CreatePriceListItemInput, Object.values(priceMap));
  }
}
