import { Modu<PERSON> } from "@nestjs/common";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { CampaignModule } from "src/campaign/campaign.module";
import { CommonModule } from "src/common/common.module";
import { PriceListItemModule } from "src/price-list-item/price-list-item.module";
import FileInput from "./dto/graphql/file.input";
import { PriceListResolver } from "./price-list.resolver";
import { PriceListRepository } from "./repositories/price-list.repository";
import { PriceListParserService } from "./services/price-list-file-parser.service";
import { PriceListHistoryService } from "./services/price-list-history.service";
import { PriceListService } from "./services/price-list.service";

@Module({
  imports: [
    CampaignModule,
    ClientsModule.register([
      {
        name: "PRODUCTS_SERVICE",
        transport: Transport.TCP,
        options: { port: 3006 },
      },
    ]),
    PriceListItemModule,
    CommonModule,
  ],
  providers: [
    PriceListResolver,
    PriceListService,
    PriceListRepository,
    PriceListHistoryService,
    FileInput,
    PriceListParserService,
  ],
  exports: [PriceListService],
})
export class PriceListModule {}
