import { Injectable } from "@nestjs/common";
import { plainToInstance } from "class-transformer";
import { PrismaService } from "src/common/services/prisma.service";
import { PriceListItem } from "src/models/price-list-item.entity";
import { CreatePriceListItemInput } from "src/price-list-item/dto/price-list-item/create-price-list-item.input";

@Injectable()
export class PriceListItemRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(
    priceListItem: CreatePriceListItemInput,
  ): Promise<PriceListItem> {
    const createResult = await this.prisma.priceListItem.create({
      data: {
        product_name: priceListItem.productName,
        sku: priceListItem.sku,
        market: priceListItem.market,
        compare_at: priceListItem.compareAt,
        price: priceListItem.price,
        price_list: {
          connect: {
            id: priceListItem.priceListId,
          },
        },
      },
    });

    return plainToInstance(PriceListItem, createResult);
  }

  async findAll(): Promise<PriceListItem[]> {
    const priceListItems = await this.prisma.priceListItem.findMany();

    return plainToInstance(PriceListItem, priceListItems);
  }
}
