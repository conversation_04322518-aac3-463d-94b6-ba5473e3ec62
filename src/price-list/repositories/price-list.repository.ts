import { Injectable } from "@nestjs/common";
import { plainToInstance } from "class-transformer";
import { PaginationParameters } from "src/common/dto/pagination-parameters.dto";
import { PrismaService } from "src/common/services/prisma.service";
import { PriceListHistory } from "src/models/price-list-history.entity";
import { PriceList } from "src/models/price-list.entity";
import { CreatePriceListItemInput } from "src/price-list-item/dto/price-list-item/create-price-list-item.input";
import { CreatePriceListInput } from "../dto/graphql/create-price-list.input";
import { PriceListHistoryWhereInput } from "../dto/graphql/price-list-history-where.input";
import PriceListUniqueInput from "../dto/graphql/price-list-unique.input";
import { PriceListWhereInput } from "../dto/graphql/price-list-where.input";
import { UpdatePriceListInput } from "../dto/graphql/update-price-list.input";

@Injectable()
export class PriceListRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(
    priceList: CreatePriceListInput,
    priceListItems: CreatePriceListItemInput[] = [],
  ): Promise<PriceList> {
    const createResult = await this.prisma.priceList.create({
      data: {
        title: priceList.title,
        scheduled_at: priceList.scheduledAt,
        comment: priceList.comment,
        campaign: {
          connect: {
            id: priceList.campaignId,
          },
        },
        price_list_items: {
          create: priceListItems.map((item) => ({
            product_name: item.productName,
            sku: item.sku,
            market: item.market,
            compare_at: item.compareAt,
            price: item.price,
          })),
        },
      },
    });
    return plainToInstance(PriceList, createResult);
  }

  async findOne(where: PriceListUniqueInput): Promise<PriceList> {
    const priceLists = await this.prisma.priceList.findUnique({
      where,
    });

    return plainToInstance(PriceList, priceLists);
  }

  async findAll(where?: PriceListWhereInput): Promise<PriceList[]> {
    const priceLists = await this.prisma.priceList.findMany({
      ...(where && {
        where: {
          campaign_id: where.campaignId,
        },
      }),
    });

    return plainToInstance(PriceList, priceLists);
  }

  async findAllPaginated(
    pagination: PaginationParameters,
    where?: PriceListWhereInput,
  ): Promise<{ priceLists: PriceList[]; totalCount: number }> {
    const query = {
      ...(where && {
        where: {
          campaign_id: where.campaignId,
        },
      }),
    };

    const [totalCount, priceLists] = await Promise.all([
      this.prisma.priceList.count(query),
      this.prisma.priceList.findMany({
        ...query,
        ...pagination,
      }),
    ]);

    return { priceLists: plainToInstance(PriceList, priceLists), totalCount };
  }

  async update(id: number, data: UpdatePriceListInput): Promise<PriceList> {
    const updateResult = await this.prisma.priceList.update({
      where: {
        id,
      },
      data: {
        title: data.title,
        scheduled_at: data.scheduledAt,
        status: data.status as string,
        comment: data.comment,
        campaign: {
          connect: {
            id: data.campaignId,
          },
        },
      },
    });

    return plainToInstance(PriceList, updateResult);
  }

  async remove(id: number): Promise<PriceList> {
    const removed = await this.prisma.priceList.delete({
      where: { id },
    });
    return plainToInstance(PriceList, removed);
  }

  async getHistories(
    where: PriceListHistoryWhereInput,
  ): Promise<PriceListHistory[]> {
    const histories = await this.prisma.priceListHistory.findMany({
      where: {
        price_list_id: where.priceListId,
      },
    });

    return plainToInstance(PriceListHistory, histories);
  }
}
