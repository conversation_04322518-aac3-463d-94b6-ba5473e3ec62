import "reflect-metadata";
import {
  YogaFederationDriver,
  YogaFederationDriverConfig,
} from "@graphql-yoga/nestjs-federation";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { APP_GUARD } from "@nestjs/core";
import { GraphQLModule } from "@nestjs/graphql";
import { CampaignModule } from "./campaign/campaign.module";
import { CommonModule } from "./common/common.module";
import { HealthController } from "./health/health.controller";
import { AuthModule } from "./auth/auth.module";
import { CognitoAuthGuard } from "./auth/cognito-auth.guard";
import { MicroserviceController } from "./microservice/microservice.controller";
import { PriceListModule } from "./price-list/price-list.module";
import { PriceListItemModule } from "./price-list-item/price-list-item.module";

@Module({
  imports: [
    ConfigModule.forRoot(),
    GraphQLModule.forRoot<YogaFederationDriverConfig>({
      driver: YogaFederationDriver,
      typePaths: ["**/*.graphql"],
      path: "/api/pricingmanagement/graphql", // Explicit path relative to global prefix
      graphiql: process.env.NODE_ENV !== "production",
    }),
    PriceListModule,
    PriceListItemModule,
    CampaignModule,
    AuthModule,
    CommonModule,
  ],
  controllers: [HealthController, MicroserviceController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: CognitoAuthGuard,
    },
  ],
})
export class AppModule {}
