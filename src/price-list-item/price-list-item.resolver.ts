import { Resolver, Query, Args, ResolveField } from "@nestjs/graphql";
import { PaginationInput } from "src/common/dto/pagination.input";
import { PaginatedPriceListItem } from "./dto/paginated-price-list-item.dto";
import { PriceListItemWhereInput } from "./dto/price-list-item-where.input";
import { PriceListItemService } from "./services/price-list-item.service";

@Resolver("PriceListItem")
export class PriceListItemResolver {
  constructor(private readonly priceListItemsService: PriceListItemService) {}

  @Query("priceListItems")
  async findAll(
    @Args("where") where: PriceListItemWhereInput,
    @Args("pagination") pagination: PaginationInput,
    @Args("quickSearchQuery", { nullable: true }) quickSearchQuery?: string,
  ): Promise<PaginatedPriceListItem> {
    return this.priceListItemsService.findAllPaginated(
      pagination,
      where,
      quickSearchQuery,
    );
  }

  @ResolveField("discountPercentage")
  priceListItems() {
    return 0;
  }
}
