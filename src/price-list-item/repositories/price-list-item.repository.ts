import { Injectable } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { plainToInstance } from "class-transformer";
import { PrismaService } from "../../common/services/prisma.service";
import { QueryMapperService } from "../../common/services/query-mapper.service";
import { PriceListItem } from "../../models/price-list-item.entity";
import { CreatePriceListItemInput } from "../dto/price-list-item/create-price-list-item.input";
import { IPriceListItemRawResult } from "../dto/price-list-item/price-list-item-raw-result";
import { PriceListItemWhereInput } from "../dto/price-list-item-where.input";

@Injectable()
export class PriceListItemRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queryMapperService: QueryMapperService,
  ) {}

  async create(
    priceListItem: CreatePriceListItemInput,
  ): Promise<PriceListItem> {
    const createResult = await this.prisma.priceListItem.create({
      data: {
        product_name: priceListItem.productName,
        sku: priceListItem.sku,
        market: priceListItem.market,
        compare_at: priceListItem.compareAt,
        price: priceListItem.price,
        price_list: {
          connect: {
            id: priceListItem.priceListId,
          },
        },
      },
    });

    return plainToInstance(PriceListItem, createResult);
  }

  async findAll(
    whereInput?: PriceListItemWhereInput,
  ): Promise<PriceListItem[]> {
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput, ["priceListId"])
      : undefined;

    const priceListItems = await this.prisma.priceListItem.findMany({ where });

    return plainToInstance(PriceListItem, priceListItems);
  }

  async findAllPaginated(
    paginationParams: { skip: number; take: number },
    whereInput?: PriceListItemWhereInput,
    quickSearchQuery?: string,
  ): Promise<{ totalCount: number; priceListItems: PriceListItem[] }> {
    if (quickSearchQuery !== undefined) {
      return await this.searchPriceListItems(
        paginationParams,
        quickSearchQuery,
        whereInput,
      );
    }

    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput, ["priceListId"])
      : undefined;

    const query = {
      where,
    };

    const [totalCount, priceListItems] = await Promise.all([
      this.prisma.priceListItem.count(query),
      this.prisma.priceListItem.findMany({ ...query, ...paginationParams }),
    ]);

    return {
      totalCount,
      priceListItems: plainToInstance(PriceListItem, priceListItems),
    };
  }

  private async searchPriceListItems(
    paginationParams: { skip: number; take: number },
    quickSearchQuery: string,
    where?: PriceListItemWhereInput,
  ): Promise<{ totalCount: number; priceListItems: PriceListItem[] }> {
    const { skip: offset, take: limit } = paginationParams;

    // Clean and parse the query string into tsQuery
    const cleanedQuery = quickSearchQuery
      .trim()
      .replace(/[^\w\s]/g, " ")
      .replace(/\s+/g, " ");

    const searchTerms = cleanedQuery
      .split(" ")
      .filter((term) => term.length > 0);

    const tsQuery = searchTerms.map((word) => `${word}:*`).join(" & ");

    // Build dynamic WHERE clause using Prisma.sql
    const whereClauses: Prisma.Sql[] = where
      ? this.queryMapperService.mapWhereInputToSQL(where, ["priceListId"])
      : [];

    whereClauses.push(
      Prisma.sql`search_vector @@ to_tsquery('english', ${tsQuery})`,
    );

    const whereSQL =
      whereClauses.length > 0
        ? Prisma.sql`WHERE ${Prisma.join(whereClauses, " AND ")}`
        : Prisma.empty;

    // Raw SQL queries with safe parameter binding
    const [results, countResult] = await this.prisma.$transaction([
      this.prisma.$queryRaw<IPriceListItemRawResult[]>(Prisma.sql`
        SELECT pli.id, pli.sku, pli.product_name, pli.market, pli.price, pli.created_at, pli.updated_at, pli.compare_at
        FROM "price_list_items" pli
        ${whereSQL}
        ORDER BY pli.id ASC
        LIMIT ${limit} OFFSET ${offset}
      `),
      this.prisma.$queryRaw<{ count: number }[]>(Prisma.sql`
        SELECT COUNT(*)
        FROM "price_list_items" pli
        ${whereSQL}
      `),
    ]);

    const totalCount = parseInt(countResult[0]?.count?.toString?.() ?? "0");

    return {
      priceListItems: plainToInstance(PriceListItem, results),
      totalCount,
    };
  }
}
