import { Injectable } from "@nestjs/common";
import { PaginationInput } from "../../common/dto/pagination.input";
import { PaginationService } from "../../common/services/pagination.service";
import { PaginatedPriceListItem } from "../dto/paginated-price-list-item.dto";
import { CreatePriceListItemInput } from "../dto/price-list-item/create-price-list-item.input";
import { PriceListItemWhereInput } from "../dto/price-list-item-where.input";
import { PriceListItemRepository } from "../repositories/price-list-item.repository";

@Injectable()
export class PriceListItemService {
  constructor(
    private readonly priceListItemRepository: PriceListItemRepository,
    private readonly paginationService: PaginationService,
  ) {}

  async create(createPriceListItemInput: CreatePriceListItemInput) {
    return this.priceListItemRepository.create(createPriceListItemInput);
  }

  async findAll(where?: PriceListItemWhereInput) {
    return this.priceListItemRepository.findAll(where);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput?: PriceListItemWhereInput,
    quickSearchQuery?: string,
  ): Promise<PaginatedPriceListItem> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);

    const { totalCount, priceListItems: data } =
      await this.priceListItemRepository.findAllPaginated(
        paginationParams,
        whereInput,
        quickSearchQuery,
      );

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );
    return { meta, data };
  }
}
