import { Test, TestingModule } from "@nestjs/testing";
import { PaginationInput } from "../../common/dto/pagination.input";
import { PaginationService } from "../../common/services/pagination.service";
import { PriceListItemRepository } from "../repositories/price-list-item.repository";
import { PriceListItemService } from "./price-list-item.service";

describe("PriceListItemService", () => {
  let priceListItemService: PriceListItemService;
  let priceListItemRepository: PriceListItemRepository;
  let paginationService: PaginationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PriceListItemService,
        {
          provide: PriceListItemRepository,
          useValue: {
            findAllPaginated: jest.fn(),
          },
        },
        {
          provide: PaginationService,
          useValue: {
            getPaginationParams: jest.fn(),
            getPaginationMeta: jest.fn(),
          },
        },
      ],
    }).compile();

    priceListItemService =
      module.get<PriceListItemService>(PriceListItemService);
    priceListItemRepository = module.get<PriceListItemRepository>(
      PriceListItemRepository,
    );
    paginationService = module.get<PaginationService>(PaginationService);
  });

  describe("searchPriceListItems", () => {
    it("should search for price list items", async () => {
      const quickSearchQuery = "test";
      const pagination: PaginationInput = { page: 1, pageSize: 10 };
      const paginationParams = { skip: 0, take: 10 };
      const totalCount = 100;
      const priceListItems = [
        {
          id: 1,
          sku: "sku1",
          productName: "product1",
          market: "market1",
          price: 10,
          compareAt: 10,
          priceListId: 1,
        },
        {
          id: 2,
          sku: "sku2",
          productName: "product2",
          market: "market2",
          price: 20,
          compareAt: 20,
          priceListId: 2,
        },
      ];

      (paginationService.getPaginationParams as jest.Mock).mockReturnValue(
        paginationParams,
      );
      (priceListItemRepository.findAllPaginated as jest.Mock).mockResolvedValue(
        { totalCount, priceListItems },
      );
      (paginationService.getPaginationMeta as jest.Mock).mockReturnValue({
        totalCount,
        currentPage: 1,
        totalPages: 10,
        hasNextPage: true,
        hasPreviousPage: false,
      });

      const result = await priceListItemService.findAllPaginated(
        pagination,
        undefined,
        quickSearchQuery,
      );

      expect(result).toEqual({
        meta: {
          totalCount,
          currentPage: 1,
          totalPages: 10,
          hasNextPage: true,
          hasPreviousPage: false,
        },
        data: priceListItems,
      });
    });
  });
});
