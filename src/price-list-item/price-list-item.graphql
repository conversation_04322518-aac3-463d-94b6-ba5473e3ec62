enum MARKET {
  AT
  AU
  CA
  COM
  DE
  EU
  FR
  IE
  UK
  US
}

type PaginatedPriceListItem {
  data: [PriceListItem!]!
  meta: PaginationMeta!
}

type PriceListItem {
  id: Int!
  sku: String!
  productName: String!
  market: MARKET!
  price: Float!
  compareAt: Float!
  discountPercentage: Float!
  priceListId: Int
}

input PriceListItemWhereInput {
  priceListId: WhereInput
  market: WhereInput
}

type Query {
  priceListItems(
    where: PriceListItemWhereInput
    quickSearchQuery: String
    pagination: PaginationInput
  ): PaginatedPriceListItem!
}
