import { <PERSON>du<PERSON> } from "@nestjs/common";
import { CommonModule } from "src/common/common.module";
import { PrismaService } from "src/common/services/prisma.service";
import { PriceListItemResolver } from "./price-list-item.resolver";
import { PriceListItemRepository } from "./repositories/price-list-item.repository";
import { PriceListItemService } from "./services/price-list-item.service";

@Module({
  imports: [CommonModule],
  exports: [PriceListItemService, PriceListItemRepository],
  providers: [
    PriceListItemResolver,
    PriceListItemService,
    PriceListItemRepository,
    PrismaService,
  ],
})
export class PriceListItemModule {}
