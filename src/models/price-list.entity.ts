import { Expose, Type } from "class-transformer";
import BaseEntity from "./base.entity";

enum PriceListStatus {
  PUBLISHED = "PUBLISHED",
  FAILED = "FAILED",
  PROCESSING = "PROCESSING",
  DRAFT = "DRAFT",
}

export class PriceList extends BaseEntity {
  @Expose()
  title: string;

  @Expose({ name: "scheduled_at" })
  @Type(() => Date)
  scheduledAt: Date;

  @Expose()
  status: PriceListStatus;

  @Expose({ name: "campaign_id" })
  campaignId: number;

  @Expose()
  comment: string;
}

export interface IPriceList {
  id: number;
  title: string;
  created_at: Date;
  scheduled_at: Date;
  status: PriceListStatus;
  campaign_id: number;
}
