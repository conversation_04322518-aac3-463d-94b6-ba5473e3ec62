import { Expose } from "class-transformer";
import BaseEntity from "./base.entity";
import { PriceListHistory } from "./price-list-history.entity";
import { PriceList } from "./price-list.entity";

export class PriceListItemHistory extends BaseEntity {
  status: string;

  price: number;

  @Expose({ name: "compare_at" })
  compareAt: number;

  market: string;

  @Expose({ name: "price_list_item_id" })
  priceListItemId: number;

  @Expose({ name: "price_list_history" })
  priceListHistory: PriceListHistory;

  @Expose({ name: "price_list_item" })
  priceListItem: PriceList;
}
