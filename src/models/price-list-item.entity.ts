import { Expose, Type } from "class-transformer";
import { IsEnum } from "class-validator";
import BaseEntity from "./base.entity";
import { PriceList } from "./price-list.entity";

enum Market {
  COM = "COM",
  US = "US",
  EU = "EU",
  UK = "UK",
  AU = "AU",
  CA = "CA",
  DE = "DE",
  FR = "FR",
  AT = "AT",
  IE = "IE",
}

export class PriceListItem extends BaseEntity {
  @Expose()
  sku: string;

  @Expose({ name: "product_name" })
  productName: string;

  @Expose()
  @IsEnum(Market)
  market: Market;

  @Expose({ name: "compare_at" })
  compareAt: number;

  @Expose()
  price: number;

  @Expose({ name: "price_list_id" })
  priceListId: number;

  @Expose({ name: "price_list" })
  @Type(() => PriceList)
  priceList: PriceList;
}
