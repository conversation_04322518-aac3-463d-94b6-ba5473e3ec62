scalar File
scalar DateTime

input PaginationInput {
  page: Int!
  pageSize: Int!
}

type PaginationMeta {
  totalCount: Int!
  currentPage: Int!
  totalPages: Int!
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
}

type PaginatedPriceListItem {
  data: [PriceListItem!]!
  meta: PaginationMeta!
}

type PaginatedPriceList {
  data: [PriceList!]!
  meta: PaginationMeta!
}

enum WhereOperator {
  is
  gte
  lte
}

input WhereInput {
  operation: WhereOperator!
  value: String!
}