import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { GqlExecutionContext } from "@nestjs/graphql";
import { ICognitoUser } from "./cognito-jwt.strategy";

export const CurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext): ICognitoUser => {
    // Handle both REST and GraphQL contexts
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req || context.switchToHttp().getRequest();
    return request.user as ICognitoUser;
  },
);
