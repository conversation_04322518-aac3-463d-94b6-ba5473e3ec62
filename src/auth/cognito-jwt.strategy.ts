import { Injectable, Logger, UnauthorizedException } from "@nestjs/common";
import { Request } from "express";
import { CognitoJwtVerifier } from "aws-jwt-verify";
import { cognitoConfig } from "./cognito.config";

export interface ICognitoUser {
  sub: string;
  email: string;
  email_verified?: boolean;
  exp: number;
  iat: number;
  token_use?: "access" | "id";
}

@Injectable()
export class CognitoAuthService {
  private readonly logger = new Logger(CognitoAuthService.name);
  private accessTokenVerifier: ReturnType<typeof CognitoJwtVerifier.create>;
  private idTokenVerifier: ReturnType<typeof CognitoJwtVerifier.create>;

  constructor() {
    // Create verifiers for both token types
    this.accessTokenVerifier = CognitoJwtVerifier.create({
      userPoolId: cognitoConfig.userPoolId,
      tokenUse: "access",
      clientId: cognitoConfig.clientId,
    });

    this.idTokenVerifier = CognitoJwtVerifier.create({
      userPoolId: cognitoConfig.userPoolId,
      tokenUse: "id",
      clientId: cognitoConfig.clientId,
    });
  }

  extractTokensFromRequest(request: Request): {
    accessToken: string;
    idToken: string;
  } {
    const cookies: Record<string, string | undefined> = request.cookies;

    const { idToken, accessToken } = Object.entries(cookies).reduce(
      (acc, [key, value]) => {
        if (key.includes("idToken")) {
          acc.idToken = value ?? "";
        } else if (key.includes("accessToken")) {
          acc.accessToken = value ?? "";
        }
        return acc;
      },
      { idToken: "", accessToken: "" },
    );

    return { accessToken, idToken };
  }

  async verifyTokens(
    accessToken: string,
    idToken: string,
  ): Promise<ICognitoUser> {
    try {
      // Verify access token (primary authorization)
      const accessPayload = await this.accessTokenVerifier.verify(accessToken);
      let email = accessPayload.email; // Might be undefined in access token
      let emailVerified = accessPayload.email_verified;

      // If we have ID token, verify it and get email from there
      if (idToken.length > 0) {
        try {
          const idPayload = await this.idTokenVerifier.verify(idToken);
          email = idPayload.email; // ID token always has email
          emailVerified = idPayload.email_verified;
        } catch (error) {
          this.logger.warn(
            "ID token verification failed, using access token only:",
            error,
          );
        }
      }

      // Ensure we have an email
      if (typeof email !== "string" || email.length === 0) {
        throw new Error("No email found in tokens - ID token may be required");
      }

      return {
        sub: accessPayload.sub,
        email: email,
        email_verified: emailVerified as boolean,
        exp: accessPayload.exp,
        iat: accessPayload.iat,
        token_use: accessPayload.token_use,
      };
    } catch (error) {
      console.error("Error verifying tokens:", error);
      throw new UnauthorizedException("Invalid tokens");
    }
  }

  /**
   * Get authenticated user from request
   */
  async getUserFromRequest(request: Request): Promise<ICognitoUser | null> {
    const { accessToken, idToken } = this.extractTokensFromRequest(request);

    if (accessToken.length === 0) {
      return null;
    }

    try {
      return await this.verifyTokens(accessToken, idToken);
    } catch {
      return null;
    }
  }
}
