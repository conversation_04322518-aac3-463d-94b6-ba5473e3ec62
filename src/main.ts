import { Logger, ValidationPipe } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { Transport, MicroserviceOptions } from "@nestjs/microservices";
import * as cookieParser from "cookie-parser";
import { AppModule } from "./app.module";

const logger = new Logger();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable cookie parsing
  app.use(cookieParser());

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  );

  app.enableCors({
    origin: true, // Allow all origins for development
    credentials: true, // Allow cookies to be sent
  });

  app.setGlobalPrefix("/api/pricingmanagement");

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: "0.0.0.0", // Listen on all interfaces in container
      port: parseInt(process.env.MICROSERVICE_PORT ?? "3003", 10),
    },
  });

  await app.startAllMicroservices();

  const httpPort = parseInt(process.env.PORT ?? "3000", 10);
  await app.listen(httpPort, "0.0.0.0");

  logger.log(`🚀 HTTP API running on port ${httpPort}`);
  logger.log(`📍 Global prefix: /api/pricingmanagement`);
  logger.log(`🎯 GraphQL endpoint: /api/pricingmanagement/graphql`);
  logger.log(`❤️ Health endpoint: /api/pricingmanagement/health`);

  if (process.env.NODE_ENV === "production") {
    logger.log(
      `🌐 Service Discovery: ${process.env.SERVICE_NAME}.${process.env.SERVICE_DISCOVERY_NAMESPACE}`,
    );
  }
}

bootstrap().catch((error) => {
  logger.error("Failed to start application:", error);
  process.exit(1);
});
